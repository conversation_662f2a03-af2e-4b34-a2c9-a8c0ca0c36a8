package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TrackList {

    @ApiModelProperty(value = "雷达站的唯一ID", required = true)
    private String entityId;

    @ApiModelProperty(value = "雷达站名称", required = true)
    private String entityName;

    @ApiModelProperty(value = "雷达站阵营", required = true)
    private String side;

    @ApiModelProperty(value = "雷达站位置", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "雷达站的探测结果", required = true)
    private List<Track> tracks = new ArrayList<>();
}
