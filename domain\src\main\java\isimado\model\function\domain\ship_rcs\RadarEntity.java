package isimado.model.function.domain.ship_rcs;

import imodel.jsim.function.common.FAAS_Antenna;
import imodel.jsim.function.common.StandardAntennaPatternAntenna;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.utils.ut.UtEntity;
import lombok.Getter;
import morpheus.service.gis.GisService;

@Getter
public class RadarEntity {

    private final RadarStatus radarStatus;

    private final UtEntity utEntity;

    private final AesStandardAntennaPattern pattern;

    private final FAAS_Antenna antenna;

    public RadarEntity(RadarStatus radarStatus, AesStandardAntennaPattern pattern){
        this.radarStatus = radarStatus;

        double elevation = GisService.INSTANCE
                .queryElevation(radarStatus.getLocationLLA().getLongitude(), radarStatus.getLocationLLA().getLatitude());

        if (radarStatus.getLocationLLA().getAltitude()<elevation){
            radarStatus.getLocationLLA().setAltitude(elevation+1d);
        }

        utEntity = new UtEntity();
        LocationLLA locationLLA = radarStatus.getLocationLLA();
        utEntity.SetLocationLLA(locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude());
        utEntity.SetOrientationNED(0,0,0);

        this.pattern =pattern;
        antenna = new StandardAntennaPatternAntenna(radarStatus.getAziScanStart(),radarStatus.getAziScanEnd(),
                -90,90,pattern);
    }

    public String getEntityId(){
        return radarStatus.getEntityId();
    }

    public String getEntityName(){
        return radarStatus.getEntityName();
    }

    public String getSide(){
        return radarStatus.getSide();
    }

    public UtEntity getUtEntity(){
        return utEntity;
    }

    public FAAS_Antenna getAntenna(){
        return antenna;
    }

}
