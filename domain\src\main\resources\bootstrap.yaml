spring:
  application:
    name: isimado-function
  main:
    allow-circular-references: true

# Knife4j配置
knife4j:
  enable: on
  setting:
    language: zh_cn

#time-out: open
service:
  ship_rcs:
    radar:
      angleResolution: 1
      bandWidth: 1e6
      pulseWidth: 1e-6
      pulseCompressionRatio: 5
      pulseRepetitionFrequency: 1.5e3
      ##
      frequency: 1.5e9
      power: 42000
      noisePower: -97
      detectionThreshold: 3
      peakGain: 35
      beamWidth: 1
      antHeight: 10
    rcs:
      top: 5
      left: 5
      front: 0.25
      backend: 0.5