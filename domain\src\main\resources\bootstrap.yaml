spring:
  application:
    name: isimado-function
  main:
    allow-circular-references: true

# Knife4j配置
knife4j:
  enable: on
  setting:
    language: zh_cn

#time-out: open
service:
  ship_rcs:
    radar:
      #角分辨率
      angleResolution: 1
      #波束宽度  主波瓣的半功率波瓣宽度
      beamWidth: 1
      #带宽 雷达信号的频率范围宽度
      bandWidth: 1e6
      #脉冲宽度  距离分辨率
      pulseWidth: 1e-6
      pulseCompressionRatio: 5
      pulseRepetitionFrequency: 1.5e3
      ##雷达工作频率
      frequency: 1.5e9
      power: 42000
      #
      noisePower: -97
      detectionThreshold: 3
      peakGain: 35

      antHeight: 50
    rcs:
      top: 5
      left: 5
      front: 0.25
      backend: 0.5