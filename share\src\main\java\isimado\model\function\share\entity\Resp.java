package isimado.model.function.share.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Data
public class Resp<T> implements Serializable {
    public static final String SUCCESS_CODE = "0000";
    public static final String SUCCESS_MSG = "success";

    /**
     * 业务状态码
     */
    private String code;
    /**
     * 信息
     */
    private String msg;

    private String type;
    /**
     * 返回的数据体
     */
    private T data;


    public static void assertSuccess(Resp resp, Supplier<String> supplier) {
        if (resp != null && SUCCESS_CODE.equals(resp.getCode())) {
            return;
        }
        throw new IllegalStateException(supplier.get());
    }

    public static void assertSuccess(Resp resp, String message) {
        assertSuccess(resp, message::toString);
    }

    public static <E> Resp<E> success(E data) {
        Resp<E> resultBase = new Resp<>();
        resultBase.setCode(SUCCESS_CODE);
        resultBase.setMsg(SUCCESS_MSG);
        resultBase.setData(data);
        resultBase.setType("xxxx");
        return resultBase;
    }

    public static <E> Resp<E> success() {
        return success(null);
    }

    /**
     * 兼容老项目代码所提供方法
     */
    public Resp<T> msg(String msg) {
        this.msg = msg;
        return this;
    }

    /**
     * 兼容老项目代码所提供方法
     */
    public static <E> Resp<E> failure() {
        return failure("9999","failed");
    }

    public static <E> Resp<E> failure(String code, String errorMsg) {
        Resp<E> body = new Resp<>();
        body.setCode(code);
        body.setMsg(errorMsg);
        return body;
    }

}
