package isimado.model.function.domain.controller;


import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import isimado.model.function.share.entity.Resp;
import isimado.model.function.domain.service.RadarFunctionService;
import isimado.model.function.share.entity.Radar2DRangeComputeDTO;
import isimado.model.function.share.entity.Radar3DRangeComputeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "雷达包络")
@ApiSupport(order = 1, author = "clyu")
@RestController
@Slf4j
@RequestMapping("radar")
public class RadarRangeComputeController {

    @Resource
    private RadarFunctionService radarFunctionService;

    @ApiOperation(value = "雷达包络2D")
    @ApiOperationSupport(order = 100)
    @PostMapping("2D")
    public Resp<double[][]> range2D(@RequestBody Radar2DRangeComputeDTO dto) {
        return Resp.success(radarFunctionService.compute2DRange(dto));
    }




    @ApiOperation(value = "雷达包络3D")
    @ApiOperationSupport(order = 100)
    @PostMapping("3D")
    public Resp<double[][]> range3D(@RequestBody Radar3DRangeComputeDTO dto) {
        return Resp.success(radarFunctionService.compute3DRange(dto));
    }






}
