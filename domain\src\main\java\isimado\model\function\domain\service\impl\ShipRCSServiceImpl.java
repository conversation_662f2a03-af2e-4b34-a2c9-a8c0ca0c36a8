package isimado.model.function.domain.service.impl;

import imodel.jsim.function.common.FAASTwoWayInteraction;
import imodel.jsim.function.common.FAAS_ITU_Attenuation;
import imodel.jsim.function.common.JammerList;
import imodel.jsim.function.common.Target;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.service.ShipRCSService;
import isimado.model.function.domain.ship_rcs.*;
import jsim.basic.utils.MoverUtils;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import jsim.utils.ut.UtEntity;
import jsim.utils.ut.UtMath;
import jsim.utils.ut.UtSphericalEarth;
import jsim.utils.ut.UtVec3d;
import morpheus.model.IDevsService;
import morpheus.service.gis.GisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ShipRCSServiceImpl implements ShipRCSService {

    @Resource
    private AesEnvironment aesEnvironment;

    private Thread thread;

    @Value("${service.ship_rcs.radar.angleResolution}")
    private double angleResolution;

    @Value("${service.ship_rcs.radar.frequency}")
    private double frequency;

    @Value("${service.ship_rcs.radar.power}")
    private double power;

    @Value("${service.ship_rcs.radar.noisePower}")
    private double noisePower;

    @Value("${service.ship_rcs.radar.detectionThreshold}")
    private double detectionThreshold;

    @Value("${service.ship_rcs.radar.antHeight}")
    private double antHeight;

    @Value("${service.ship_rcs.radar.bandWidth}")
    private double bandWidth;

    @Value("${service.ship_rcs.radar.pulseWidth}")
    private double pulseWidth;

    @Value("${service.ship_rcs.radar.pulseCompressionRatio}")
    private double pulseCompressionRatio;

    @Value("${service.ship_rcs.radar.pulseRepetitionFrequency}")
    private double pulseRepetitionFrequency;

    @Resource
    private AesStandardAntennaPattern pattern;

    private SituationResponse response =null;

    private final Object lock = new Object();

    private double rangeResolution;
    @Resource
    private RCSService rcsService;


    @PostConstruct
    public void init(){
        double processedPulseWidth = pulseWidth / pulseCompressionRatio;
        rangeResolution = (UtMath.cLIGHT_SPEED * processedPulseWidth) / 2.0;
        thread = new Thread(()->{
            updateLocation();
        });
        thread.start();
    }

    @Override
    public List<TrackList> attemptDetect(RadarDetectRequest request) {
        List<TrackList> results = new ArrayList<>();
        for (RadarStatus radarStatus : request.getRadarList()) {
            List<ShipGroup> shipGroups = new ArrayList<>();
            RadarEntity radar = new RadarEntity(radarStatus, pattern);
            for (ShipStatus shipStatus : request.getShipList()) {
                ShipGroup findGroup = null;
                ShipEntity ship = new ShipEntity(shipStatus);
                for (ShipGroup shipGroup : shipGroups) {
                    if (inGroup(radar, shipGroup, ship)) {
                        findGroup = shipGroup;
                    }
                }
                if (findGroup == null) {
                    double[] locationWCS = new double[3];
                    radar.getUtEntity().GetLocationWCS(locationWCS);
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);
                    double[] doubles = radar.getUtEntity().ComputeAspect(relativeLocationWCS, 0, 0);
                    double azi = doubles[0];
                    double ele = doubles[1];
                    findGroup = new ShipGroup(azi,ele, UtVec3d.Magnitude(relativeLocationWCS));
                    shipGroups.add(findGroup);
                }
                findGroup.addShip(ship);
            }
            List<Track> tracks = attemptDetect(radar, shipGroups);
            TrackList result = new TrackList();
            result.setEntityId(radar.getEntityId());
            result.setTracks(tracks);
            results.add(result);
        }
        synchronized (lock) {
            EnvironmentStatus environmentStatus = request.getEnvironmentStatus();
            if (environmentStatus != null) {
                if (environmentStatus.getWindSpeed() !=null) {
                    aesEnvironment.mWindSpeed = environmentStatus.getWindSpeed();
                }
                if (environmentStatus.getWindDirection() != null) {
                    aesEnvironment.mWindDirection = Math.toRadians(environmentStatus.getWindDirection());
                }
                if (environmentStatus.getSeaState() !=null) {
                    aesEnvironment.mSeaState = AesEnvironment.SeaState.from(environmentStatus.getSeaState());
                }
                if (environmentStatus.getRainRate() !=null) {
                    aesEnvironment.mRainUpperAlt = 5000;
                    aesEnvironment.mRainRate = environmentStatus.getRainRate();
                }
            }


            response = new SituationResponse();
            response.setTimeStamp(System.currentTimeMillis());
            response.setTrackList(results);
            response.setRadarStatusList(request.getRadarList());
            response.setShipStatusList(request.getShipList());
        }
        return results;
    }

    public void updateLocation(){
        while (true) {
            try {
                if (response != null) {
                    synchronized (lock) {
                        long timeStamp = response.getTimeStamp();
                        long now = System.currentTimeMillis();
                        double secs = (now - timeStamp) / 1000d;
                        if (secs > 1) {
                            for (ShipStatus shipStatus : response.getShipStatusList()) {
                                double speed = shipStatus.getSpeed();
                                double[] move = MoverUtils.move(shipStatus.getLocationLLA().getLatitude(), shipStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(shipStatus.getHeading()), speed, secs);
                                shipStatus.getLocationLLA().setLatitude(move[1]);
                                shipStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            RadarDetectRequest request = new RadarDetectRequest();
                            request.setRadarList(response.getRadarStatusList());
                            request.setShipList(response.getShipStatusList());
                            attemptDetect(request);
                        }
                    }
                }

                Thread.sleep(1000);
            } catch (RuntimeException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public String getSituation() {
        synchronized (lock) {
            return JsonUtils.toJson(response);
        }
    }

    @Override
    public double[][] compute2DRange(RadarStatus radarStatus) {
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = new RadarEntity(radarStatus, pattern);
        double[] locationLLA = radar.getUtEntity().GetLocationLLA();
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(power,frequency,Math.pow(10, noisePower*0.1)/1000,
                radar.getAntenna(),Math.pow(10, detectionThreshold*0.1),locationLLA,
                Math.toRadians(0),
                Math.toRadians(0),antHeight,-1
                ,target,faasItuAttenuation);
        return interaction.compute2DRange(0, new JammerList());
    }

    @Override
    public double[][] compute3DRange(RadarStatus radarStatus) {
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);
        Target target = new Target();
        target.setRcs(10);
        RadarEntity radar = new RadarEntity(radarStatus, pattern);
        double[] locationLLA = radar.getUtEntity().GetLocationLLA();
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(power,frequency,Math.pow(10, noisePower*0.1)/1000,
                radar.getAntenna(),Math.pow(10, detectionThreshold*0.1),locationLLA,
                Math.toRadians(0),
                Math.toRadians(0),antHeight,-1
                ,target,faasItuAttenuation);
        return interaction.compute3DRange(new JammerList());
    }

    /**
     * 雷达探测算法核心方法 - 对目标组进行探测判断
     *
     * 该方法实现了完整的雷达探测流程，包括：
     * 1. 大气衰减建模
     * 2. 雷达方程计算
     * 3. 杂波分析
     * 4. 地形遮挡检查
     * 5. 最终探测判断
     *
     * @param radar 雷达实体，包含雷达位置、天线参数等信息
     * @param shipGroups 目标组列表，每组包含角度/距离相近的目标
     * @return 探测到的航迹列表，每个航迹对应一个被探测到的目标组
     */
    private List<Track> attemptDetect(RadarEntity radar, List<ShipGroup> shipGroups){
        // ========== 1. 初始化大气衰减模型 ==========
        // 创建ITU大气衰减模型，用于计算电磁波在大气中的传播损耗
        // 参数：大气表、工作频率(1.5GHz)、降雨率(0)、环境参数
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                frequency, 0, aesEnvironment);

        // 初始化探测结果列表
        List<Track> tracks = new ArrayList<>();

        // ========== 2. 遍历每个目标组进行探测判断 ==========
        for (ShipGroup shipGroup : shipGroups){
            // ========== 2.1 创建目标模型 ==========
            Target target = new Target();
            // 计算目标组的总RCS（雷达截面积）
            // 考虑目标相对于雷达的角度、频率等因素
            target.setRcs(shipGroup.getRCS(rcsService,radar, frequency));

            // 获取雷达位置（纬度、经度、高度）
            double[] locationLLA = radar.getUtEntity().GetLocationLLA();

            // ========== 2.2 创建雷达-目标双向交互模型 ==========
            // 这是雷达方程的核心实现，计算雷达探测能力
            FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(
                    power,                                    // 发射功率: 42kW
                    frequency,                               // 工作频率: 1.5GHz
                    Math.pow(10, noisePower*0.1)/1000,      // 噪声功率: -97dBm转换为瓦特
                    radar.getAntenna(),                      // 天线模型(包含35dB增益、1度波束宽度)
                    Math.pow(10, detectionThreshold*0.1),   // 探测门限: 3dB转换为线性值
                    locationLLA,                             // 雷达位置
                    Math.toRadians(0),                       // 雷达方位角(0度，正北)
                    Math.toRadians(0),                       // 雷达俯仰角(0度，水平)
                    antHeight,                               // 天线高度: 10米
                    -1,                                      // 距离限制(-1表示无限制)
                    target,                                  // 目标模型
                    faasItuAttenuation                       // 大气衰减模型
            );

            // ========== 2.3 距离限制检查 ==========
            // 计算雷达在当前环境下对该高度目标的理论最大探测距离
            // 如果目标距离超过理论最大距离，则无法探测
            if ((interaction.computeLimitRange(locationLLA[2], shipGroup.getGroup().get(0).getUtEntity().mAlt)
                    > shipGroup.getRange())){

                // ========== 2.4 设置雷达脉冲参数 ==========
                // 计算接收机在目标方向的干扰功率(当前无干扰机，传入空列表)
                interaction.computeRcvrJammerPower(shipGroup.getAzimuth(), shipGroup.getElevation(), new JammerList());

                // 设置脉冲雷达的关键参数
                interaction.setBandWidth(bandWidth);                           // 信号带宽: 1MHz
                interaction.setPulseWidth(pulseWidth);                         // 脉冲宽度: 1μs
                interaction.setPulseCompressionRatio(pulseCompressionRatio);   // 脉冲压缩比: 5
                interaction.setPulseRepetitionFrequency(pulseRepetitionFrequency); // 脉冲重复频率: 1500Hz

                // ========== 2.5 海面杂波计算 ==========
                // 计算脉冲多普勒雷达的海面杂波功率
                // 杂波是影响探测性能的重要因素，特别是对海面目标
                double pulseDopplerClutter = FAAS_EMSurface_Clutter.PulseDopplerClutter(
                        interaction,           // 雷达交互模型
                        aesEnvironment,       // 环境参数(海况、风速等)
                        shipGroup.getRange()  // 目标距离
                );

                // ========== 2.6 雷达探测判断 ==========
                // 基于信噪比进行探测判断
                // 考虑目标回波功率、噪声功率、杂波功率
                boolean canDetected = interaction.attemptDetect(
                        shipGroup.getGroup().get(0).getUtEntity(), // 目标实体
                        pulseDopplerClutter                         // 杂波功率
                );

                // ========== 2.7 地形遮挡检查 ==========
                // 检查雷达与目标之间是否被地形遮挡
                UtEntity src = radar.getUtEntity();                    // 雷达位置
                UtEntity to = shipGroup.getGroup().get(0).getUtEntity(); // 目标位置

                // 进行地形遮挡快速检查
                // 参数：雷达位置、目标位置、最大距离(0=无限制)、检查步长(2500米)
                canDetected &= !MaskedByTerrainFastP(
                        src.mLat, src.mLon, src.mAlt + antHeight,  // 雷达位置(含天线高度)
                        to.mLat, to.mLon, to.mAlt,                 // 目标位置
                        0,                                          // 最大检查距离(0=无限制)
                        2500                                        // 地形检查步长(米)
                );

                // ========== 2.8 生成探测结果 ==========
                if (canDetected) {
                    // 创建新的航迹
                    Track track = new Track();
                    // 将目标组中所有目标的ID添加到航迹中
                    // 注意：由于角度分辨率限制，雷达无法区分组内的单个目标
                    track.setEntityIds(shipGroup.getGroup().stream()
                            .map(ShipEntity::getEntityId)
                            .collect(Collectors.toList()));
                    tracks.add(track);
                }
            }
            // 如果目标超出理论探测距离，直接跳过该目标组
        }

        // 返回所有探测到的航迹
        return tracks;
    }

    private boolean inGroup(RadarEntity radar, ShipGroup shipGroup, ShipEntity newTarget){
        double[] locationWCS = new double[3];
        radar.getUtEntity().GetLocationWCS(locationWCS);
        double[] tgtLocationWCS = new double[3];
        newTarget.getUtEntity().GetLocationWCS(tgtLocationWCS);

        double[] relativeLocationWCS = new double[3];
        UtVec3d.Subtract(relativeLocationWCS,tgtLocationWCS,locationWCS);
        double[] doubles = radar.getUtEntity().ComputeAspect(relativeLocationWCS, 0, 0);
        double azi = doubles[0];
        if (Math.abs(Math.toDegrees(azi)-Math.toDegrees(shipGroup.getAzimuth()))>angleResolution){
            return false;
        }

        for (ShipEntity entity : shipGroup.getGroup()) {
            entity.getUtEntity().GetLocationWCS(locationWCS);
            UtVec3d.Subtract(relativeLocationWCS,locationWCS, tgtLocationWCS);
            double distance = UtVec3d.Magnitude(relativeLocationWCS);
            if (distance<=rangeResolution){
                return true;
            }
        }
        return false;
    }

    private boolean MaskedByTerrainFastP(double aLat1, double aLon1, double aAlt1, double aLat2, double aLon2, double aAlt2,
                                         double aMaxRange, int step) {
        boolean badData = false;
        double terrainHeightF = (double)0.0F;
        double heightAboveTerrain = (double)0.0F;
        double lat1 = aLat1;
        double lon1 = aLon1;
        double alt1 = aAlt1;
        double lat2 = aLat2;
        double lon2 = aLon2;
        double alt2 = aAlt2;
        if (aAlt2 < aAlt1) {
            lat1 = aLat2;
            lon1 = aLon2;
            alt1 = aAlt2;
            lat2 = aLat1;
            lon2 = aLon1;
            alt2 = aAlt1;
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon2, lat2);
        heightAboveTerrain = alt2 - terrainHeightF;
        if (heightAboveTerrain < (double)0.0F) {
            if (heightAboveTerrain < (double)-1.0F) {
                return true;
            }

            alt2 = terrainHeightF + (double)0.1F;
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon1, lat1);
        if (terrainHeightF < (double)-500.0F) {
            badData = true;
        }

        heightAboveTerrain = alt1 - terrainHeightF;
        if (heightAboveTerrain < (double)0.0F) {
            if (heightAboveTerrain < (double)-1.0F) {
                return true;
            }

            alt1 = terrainHeightF + (double)0.1F;
        }

        if (alt2 < alt1) {
            double temp = lat1;
            lat1 = lat2;
            lat2 = temp;
            temp = lon1;
            lon1 = lon2;
            lon2 = temp;
            temp = alt1;
            alt1 = alt2;
            alt2 = temp;
        }

        if (alt1 > (double)50000.0F) {
            return false;
        } else {
            double[] posECEF1 = new double[3];
            double[] posECEF2 = new double[3];
            UtSphericalEarth.ConvertLLAToECEF(lat1, lon1, alt1, posECEF1);
            UtSphericalEarth.ConvertLLAToECEF(lat2, lon2, alt2, posECEF2);
            double[] unitSrcToDst = new double[3];
            UtVec3d.Set(unitSrcToDst, posECEF2[0] - posECEF1[0], posECEF2[1] - posECEF1[1], posECEF2[2] - posECEF1[2]);
            double srcToDstDist = UtVec3d.Magnitude(unitSrcToDst);
            if (aMaxRange > (double)0.0F && srcToDstDist >= aMaxRange) {
                return true;
            } else {
                UtVec3d.Normalize(unitSrcToDst);
                double[] currPosECEF = new double[3];
                double currDist = (double)0.0F;
                UtVec3d.Set(currPosECEF, posECEF1);

                while(true) {
                    double stepDist;
                    if (badData) {
                        stepDist = (double)30.0F;
                        badData = false;
                    } else {
                        stepDist = (double)step;
                    }

                    currDist += stepDist;
                    if (currDist >= srcToDstDist) {
                        return false;
                    }

                    currPosECEF[0] += stepDist * unitSrcToDst[0];
                    currPosECEF[1] += stepDist * unitSrcToDst[1];
                    currPosECEF[2] += stepDist * unitSrcToDst[2];
                    double[] lla = UtSphericalEarth.ConvertECEFToLLA(currPosECEF);
                    double currLat = lla[0];
                    double currLon = lla[1];
                    double currAlt = lla[2];
                    terrainHeightF = GisService.INSTANCE.queryElevation(currLon, currLat);
                    if (terrainHeightF < (double)-500.0F) {
                        badData = true;
                    } else {
                        heightAboveTerrain = currAlt - terrainHeightF;
                        if (heightAboveTerrain < (double)0.0F) {
                            return true;
                        }
                    }
                }
            }
        }
    }
}
