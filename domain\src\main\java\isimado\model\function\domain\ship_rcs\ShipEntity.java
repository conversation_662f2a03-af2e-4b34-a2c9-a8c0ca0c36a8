package isimado.model.function.domain.ship_rcs;

import jsim.utils.ut.UtEntity;

public class ShipEntity {
    private ShipStatus shipStatus;

    private UtEntity utEntity;

    public ShipEntity(ShipStatus shipStatus) {
        this.shipStatus = shipStatus;
        utEntity = new UtEntity();
        LocationLLA locationLLA = shipStatus.getLocationLLA();
        utEntity.SetLocationLLA(locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude());
        utEntity.SetOrientationNED(Math.toRadians(shipStatus.getHeading()),
                0,0);
    }

    public UtEntity getUtEntity() {
        return utEntity;
    }

    public String getEntityId(){
        return shipStatus.getEntityId();
    }

    public double getSpeed(){
        return shipStatus.getSpeed();
    }
    public String getEntityName(){
        return shipStatus.getEntityName();
    }

    public String getSide(){
        return shipStatus.getSide();
    }
}
