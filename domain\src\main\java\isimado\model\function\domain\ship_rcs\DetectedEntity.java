package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 探测到的实体信息
 */
@Data
public class DetectedEntity {
    
    @ApiModelProperty(value = "实体ID", required = true)
    private String entityId;
    
    @ApiModelProperty(value = "实体名称", required = true)
    private String entityName;
    
    @ApiModelProperty(value = "阵营标识", required = true)
    private String side;

    @ApiModelProperty(value = "位置信息", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "位置信息", required = true)
    private double speed;


}
