package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 探测到的实体信息
 */
@Data
public class DetectedEntity {
    
    @ApiModelProperty(value = "实体ID", required = true)
    private String entityId;
    
    @ApiModelProperty(value = "实体名称", required = true)
    private String entityName;
    
    @ApiModelProperty(value = "阵营标识", required = true)
    private String side;
    
    @ApiModelProperty(value = "高度", required = true)
    private Double altitude;
    
    @ApiModelProperty(value = "纬度", required = true)
    private Double latitude;
    
    @ApiModelProperty(value = "经度", required = true)
    private Double longitude;
}
